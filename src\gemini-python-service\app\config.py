from pydantic_settings import BaseSettings, SettingsConfigDict
from typing import Optional
import os

class Settings(BaseSettings):
    # Google Cloud Project Configuration - Updated with new Vertex AI credentials
    gcp_project_id: str = "vertex-ai-erp"
    gcp_location: str = "us-central1"
    gemini_model_name: str = "gemini-2.0-flash-001"

    # Legacy path to service account key file (optional)
    google_application_credentials: Optional[str] = None

    # Google Cloud Service Account Credentials - Updated with new Vertex AI service account
    google_service_account_type: Optional[str] = "service_account"
    google_service_account_project_id: Optional[str] = "vertex-ai-erp"
    google_service_account_private_key_id: Optional[str] = "d0767c2be40d1dc401462c06a724c146074006c8"
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    google_service_account_client_email: Optional[str] = "<EMAIL>"
    google_service_account_client_id: Optional[str] = "106510384129042127386"
    google_service_account_auth_uri: Optional[str] = "https://accounts.google.com/o/oauth2/auth"
    google_service_account_token_uri: Optional[str] = "https://oauth2.googleapis.com/token"
    google_service_account_auth_provider_x509_cert_url: Optional[str] = "https://www.googleapis.com/oauth2/v1/certs"
    google_service_account_client_x509_cert_url: Optional[str] = "https://www.googleapis.com/robot/v1/metadata/x509/gemini-erp%40vertex-ai-erp.iam.gserviceaccount.com"
    google_service_account_universe_domain: Optional[str] = "googleapis.com"

    model_config = SettingsConfigDict(
        # No longer reading from .env file - using direct configuration
        extra="ignore",
        env_file_encoding='utf-8'
    )

    def has_service_account_env_vars(self) -> bool:
        """Check if service account credentials are provided via environment variables"""
        return (self.google_service_account_type is not None and
                self.google_service_account_project_id is not None and
                self.google_service_account_private_key is not None and
                self.google_service_account_client_email is not None)

settings = Settings()
