# Cloud Run Configuration with Container MyS<PERSON>
# This profile is used when deploying to Google Cloud Run with containerized MySQL

# Set the server port to 8081
server.port=8081

# LLM Microservice Configuration for Cloud Run
llm.python.service.baseurl=http://gemini-python-service:8000

# Dynamics 365 Business Central Configuration
dynamics.bc.odata.base-url=https://bctest.dayliff.com:7048/BC160/ODataV4/Company('KENYA')
dynamics.bc.odata.username=webservice
dynamics.bc.odata.key=3Psyn4wULY0Xhyc+z8ABalvTKAl59H1SrX2PI5AEFnM=
dynamics.bc.odata.status-field-name=AI_Verification_Status

# SharePoint Configuration
sharepoint.client.id=56ee932a-3ceb-40db-9727-cfee3d66501f
sharepoint.client.secret=****************************************
sharepoint.tenant.id=e9e12402-b3ab-458f-a106-d7b5007b75fc

# H2 In-Memory Database Configuration for Demo
# Using H2 for quick demo without external database dependencies
spring.datasource.url=jdbc:h2:mem:aierpdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

# JPA/Hibernate Configuration for H2
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# Enable H2 Console for debugging (optional)
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# Connection Pool Configuration
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=2
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.leak-detection-threshold=60000

# Enable database initialization
app.database.initialize=true

# Logging Configuration
logging.level.root=INFO
logging.level.com.erp.aierpbackend=INFO
logging.level.org.springframework.web.reactive.function.client=WARN
logging.level.reactor.netty=WARN

# JWT Configuration
app.jwt.secret=aierpSecretKey123456789012345678901234567890123456789012345678901234567890
app.jwt.expiration-ms=86400000

# Disable RabbitMQ for Cloud Run deployment
spring.rabbitmq.enabled=false

# Actuator Configuration for Cloud Run Health Checks
management.endpoints.web.exposure.include=health,info
management.endpoint.health.enabled=true
management.endpoint.health.show-details=when-authorized

# Disable RabbitMQ for Cloud Deployment (not used)
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration

# Performance and Memory Configuration for Cloud Run
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true

# Timezone Configuration
spring.jpa.properties.hibernate.jdbc.time_zone=UTC
user.timezone=UTC
