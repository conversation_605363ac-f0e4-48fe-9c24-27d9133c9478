# Cloud SQL Configuration for Production Deployment
# This profile is used when deploying to Google Cloud Run with Cloud SQL

# Set the server port to 8081
server.port=8081

# LLM Microservice Configuration for Cloud Run
# Direct configuration for Cloud Run AI service URL
llm.python.service.baseurl=http://gemini-python-service:8000

# Dynamics 365 Business Central Configuration
dynamics.bc.odata.base-url=https://bctest.dayliff.com:7048/BC160/ODataV4/Company('KENYA')
dynamics.bc.odata.username=webservice
dynamics.bc.odata.key=3Psyn4wULY0Xhyc+z8ABalvTKAl59H1SrX2PI5AEFnM=
dynamics.bc.odata.status-field-name=AI_Verification_Status

# SharePoint Configuration - Microsoft Graph API
sharepoint.client.id=56ee932a-3ceb-40db-9727-cfee3d66501f
sharepoint.client.secret=****************************************
sharepoint.tenant.id=e9e12402-b3ab-458f-a106-d7b5007b75fc

# WebClient Configuration for Cloud Environment
spring.webflux.client.max-in-memory-size=16MB
spring.codec.max-in-memory-size=16MB

# Cloud SQL Database Configuration
# Using Google Cloud SQL MySQL instance
spring.datasource.url=jdbc:mysql://google/${CLOUD_SQL_DATABASE_NAME:aierpdb}?cloudSqlInstance=${CLOUD_SQL_CONNECTION_NAME}&socketFactory=com.google.cloud.sql.mysql.SocketFactory&useSSL=false&serverTimezone=UTC&allowPublicKeyRetrieval=true
spring.datasource.username=${CLOUD_SQL_USERNAME:erp-user}
spring.datasource.password=${CLOUD_SQL_PASSWORD}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Google Cloud SQL Connector Configuration
spring.cloud.gcp.sql.enabled=true
spring.cloud.gcp.sql.database-name=${CLOUD_SQL_DATABASE_NAME:aierpdb}
spring.cloud.gcp.sql.instance-connection-name=${CLOUD_SQL_CONNECTION_NAME}

# JPA/Hibernate Configuration for Cloud SQL
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# Connection Pool Configuration for Cloud SQL
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=2
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.leak-detection-threshold=60000

# Logging Configuration for Production
logging.level.root=INFO
logging.level.com.erp.aierpbackend=INFO
logging.level.org.springframework.web.reactive.function.client=WARN
logging.level.reactor.netty=WARN

# JWT Configuration
app.jwt.secret=aierpSecretKey123456789012345678901234567890123456789012345678901234567890
app.jwt.expiration-ms=86400000

# Disable RabbitMQ for Cloud Run deployment
spring.rabbitmq.enabled=false

# Actuator Configuration for Cloud Run Health Checks
management.endpoints.web.exposure.include=health,info
management.endpoint.health.enabled=true
management.endpoint.health.show-details=when-authorized
# Temporarily disable database health check to allow startup
management.health.db.enabled=false

# Disable RabbitMQ for Cloud Deployment (not used)
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration

# Disable Tesseract OCR for Cloud Deployment (using Gemini AI instead)
# tesseract.path is not needed in cloud environment

# Performance and Memory Configuration for Cloud Run
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true

# Timezone Configuration
spring.jpa.properties.hibernate.jdbc.time_zone=UTC
user.timezone=UTC
