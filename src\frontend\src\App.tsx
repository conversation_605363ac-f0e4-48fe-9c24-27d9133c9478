import { BrowserRouter as Router, Route, Routes, Navigate } from "react-router-dom"
import { SidebarProvider, SidebarInset } from "./components/ui/sidebar"
import { ThemeProvider } from "./components/theme-provider"
import { Toaster } from "./components/ui/sonner"
import { AppSidebar } from "./components/app-sidebar"
import Home from "./pages/home.tsx"
import Dashboard from "./pages/dashboard"
import Jobs from "./pages/Jobs"
import JobDetail from "./pages/JobDetail"
import Analytics from "./pages/analytics"
import Settings from "./pages/settings"
import JobVerification from "./pages/JobVerification"
import { MobileSidebarTrigger } from "./components/mobile-sidebar-trigger"

function App() {
  // Simple layout component - no authentication needed
  const AppLayout = ({ children }: { children: React.ReactNode }) => {
    return (
      <SidebarProvider>
        <div className="flex w-full h-screen">
          <AppSidebar />
          <SidebarInset className="flex-1 overflow-auto">
            <MobileSidebarTrigger />
            {children}
          </SidebarInset>
        </div>
        <Toaster />
      </SidebarProvider>
    )
  }

  return (
      <Router>
        <ThemeProvider defaultTheme="system" storageKey="ui-theme">
          <Routes>
            {/* Home page route */}
            <Route path="/" element={<Home onLogin={() => (window.location.href = "/dashboard")} />} />

            {/* All other routes use the app layout */}
            <Route path="/dashboard" element={<AppLayout><Dashboard /></AppLayout>} />
            <Route path="/jobs" element={<AppLayout><Jobs /></AppLayout>} />
            <Route path="/jobs/:id" element={<AppLayout><JobDetail /></AppLayout>} />
            <Route path="/analytics" element={<AppLayout><Analytics /></AppLayout>} />
            <Route path="/job-verification" element={<AppLayout><JobVerification /></AppLayout>} />
            <Route path="/settings" element={<AppLayout><Settings /></AppLayout>} />

            {/* Redirect any unknown routes to dashboard */}
            <Route path="*" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </ThemeProvider>
      </Router>
  )
}

export default App
