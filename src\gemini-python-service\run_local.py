#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to run the Gemini Python microservice locally.
This script sets up the necessary environment and runs the FastAPI application.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def setup_environment():
    """Set up the environment variables needed for the service."""
    print("Note: Configuration is now hardcoded in app/config.py")
    print("No .env file is needed anymore - credentials are embedded in the application.")

    # Set required environment variables for compatibility (these are now defaults in config.py)
    os.environ.setdefault('GCP_PROJECT_ID', 'erp-bc-459514')
    os.environ.setdefault('GCP_LOCATION', 'us-central1')
    os.environ.setdefault('GEMINI_MODEL_NAME', 'gemini-2.0-flash-001')

    print("Environment setup complete - using embedded configuration.")
    return True

def run_service(host='127.0.0.1', port=8000, reload=True):
    """Run the FastAPI service using uvicorn."""
    cmd = [
        "uvicorn", 
        "app.main:app", 
        "--host", host, 
        "--port", str(port)
    ]
    
    if reload:
        cmd.append("--reload")
    
    print(f"Starting Gemini Python microservice on http://{host}:{port}")
    subprocess.run(cmd)

def main():
    parser = argparse.ArgumentParser(description='Run the Gemini Python microservice locally')
    parser.add_argument('--host', default='127.0.0.1', help='Host to bind the server to')
    parser.add_argument('--port', type=int, default=8000, help='Port to bind the server to')
    parser.add_argument('--no-reload', action='store_true', help='Disable auto-reload on code changes')
    
    args = parser.parse_args()
    
    if not setup_environment():
        sys.exit(1)
    
    run_service(host=args.host, port=args.port, reload=not args.no_reload)

if __name__ == '__main__':
    main()
