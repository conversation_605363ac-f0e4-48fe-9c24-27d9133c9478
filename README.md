# AI ERP System

An intelligent ERP system that integrates with Microsoft Dynamics 365 Business Central and uses AI for document verification and analysis.

## Overview

This project is a comprehensive ERP solution that combines:
- **Spring Boot Backend** - RESTful API services with embedded credentials
- **React Frontend** - Modern web interface with no authentication required
- **Python AI Service** - Vertex AI-powered document analysis
- **Business Central Integration** - Real-time data synchronization
- **SharePoint Integration** - Document management

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Web     │    │  Spring Boot    │    │   Python AI     │
│   Frontend      │◄──►│    Backend      │◄──►│   Service       │
│   (Port 5173)   │    │   (Port 8081)   │    │  (Port 8000)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       ▼                       ▼
         │              ┌─────────────────┐    ┌─────────────────┐
         │              │     MySQL       │    │  Google Cloud   │
         │              │   Database      │    │   Vertex AI     │
         │              └─────────────────┘    └─────────────────┘
         │
         ▼
┌─────────────────┐    ┌─────────────────┐
│   Dynamics BC   │    │   SharePoint    │
│   Integration   │    │   Integration   │
└─────────────────┘    └─────────────────┘
```

## Features

- **Job Management** - Track and manage ERP jobs
- **Document Verification** - AI-powered document analysis using Vertex AI
- **Real-time Sync** - Live data from Business Central
- **Analytics Dashboard** - Comprehensive reporting
- **No Authentication** - Direct access to dashboard for internal use

## Technology Stack

- **Backend**: Java 17, Spring Boot 3.x, MySQL
- **Frontend**: React 18, TypeScript, Vite, Tailwind CSS
- **AI Service**: Python 3.10, FastAPI, Google Cloud Vertex AI
- **Database**: MySQL 8.0

## Prerequisites

### Required Software
- **Java 17 or higher** - [Download OpenJDK](https://openjdk.org/install/)
- **Maven 3.6+** - [Download Maven](https://maven.apache.org/download.cgi)
- **Node.js 18+** - [Download Node.js](https://nodejs.org/)
- **pnpm** - Install with `npm install -g pnpm`
- **Python 3.10+** - [Download Python](https://www.python.org/downloads/)
- **MySQL 8.0** - [Download MySQL](https://dev.mysql.com/downloads/)

## Localhost Setup Guide

### 1. Clone the Repository
```bash
git clone <repository-url>
cd Backend-B2
```

### 2. Database Setup

#### Install and Start MySQL
1. Download and install MySQL 8.0
2. Start MySQL service
3. Create the database:

```sql
-- Connect to MySQL as root
mysql -u root -p

-- Create database
CREATE DATABASE aierpdb;

-- Exit MySQL
exit;
```

**Note**: The application is configured to use:
- **Username**: `root`
- **Password**: `229T$8t6`
- **Database**: `aierpdb`
- **Port**: `3306`

If your MySQL setup differs, update the credentials in `src/backend/src/main/resources/application.properties`.

### 3. Backend Setup

```bash
# Navigate to backend directory
cd src/backend

# Install dependencies and run
mvn clean install
mvn spring-boot:run
```

**Expected Output**:
- Backend starts on `http://localhost:8081`
- Database tables are created automatically
- Business Central and SharePoint connections are established

### 4. Python AI Service Setup

```bash
# Navigate to Python service directory
cd src/gemini-python-service

# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Run the service
python run_local.py
```

**Expected Output**:
- AI service starts on `http://localhost:8000`
- Vertex AI credentials are loaded automatically
- Service is ready to process AI requests

### 5. Frontend Setup

```bash
# Navigate to frontend directory
cd src/frontend

# Install dependencies
pnpm install

# Start development server
pnpm run dev
```

**Expected Output**:
- Frontend starts on `http://localhost:5173`
- Automatically opens in your browser
- No login required - click "Login" button to go directly to dashboard

## Accessing the Application

1. **Open your browser** and go to `http://localhost:5173`
2. **Click the "Login" button** - you'll be redirected directly to the dashboard
3. **Explore the features**:
   - Dashboard overview
   - Jobs management
   - Document verification
   - Analytics
   - Settings

## Configuration

All credentials are embedded in the application code for easy setup:

### Database Configuration
- **Host**: `localhost:3306`
- **Database**: `aierpdb`
- **Username**: `root`
- **Password**: `229T$8t6`

### External Services (Pre-configured)
- **Business Central API**: `https://bctest.dayliff.com:7048/BC160/ODataV4/Company('KENYA')`
- **SharePoint**: Azure AD integration
- **Vertex AI**: Google Cloud project `vertex-ai-erp`

## API Endpoints

### Backend API (`http://localhost:8081/api`)
- `GET /jobs` - List all jobs
- `GET /jobs/{id}` - Get job details
- `POST /verification/verify` - Verify documents
- `GET /actuator/health` - Health check

### AI Service API (`http://localhost:8000`)
- `POST /analyze-document` - Analyze documents with AI
- `GET /health` - Health check

## Development Commands

### Backend
```bash
cd src/backend
mvn clean install    # Build
mvn spring-boot:run   # Run
mvn test             # Test
```

### Frontend
```bash
cd src/frontend
pnpm install         # Install dependencies
pnpm run dev         # Development server
pnpm run build       # Build for production
pnpm run preview     # Preview production build
```

### Python Service
```bash
cd src/gemini-python-service
pip install -r requirements.txt  # Install dependencies
python run_local.py              # Run service
python -m pytest                 # Run tests
```

## Troubleshooting

### Common Issues

#### 1. Database Connection Error
```
Error: Could not connect to MySQL
```
**Solution**:
- Ensure MySQL is running: `sudo systemctl start mysql` (Linux) or start MySQL service (Windows)
- Verify credentials in `src/backend/src/main/resources/application.properties`
- Check if database `aierpdb` exists

#### 2. Frontend Can't Connect to Backend
```
Error: Network Error
```
**Solution**:
- Ensure backend is running on port 8081
- Check if `http://localhost:8081/actuator/health` returns OK
- Verify no firewall is blocking the connection

#### 3. AI Service Authentication Error
```
Error: Vertex AI authentication failed
```
**Solution**:
- Verify the `vertex-ai-erp-d0767c2be40d.json` file exists in the root directory
- Check that credentials in `src/gemini-python-service/app/config.py` match the JSON file
- Ensure the Vertex AI project has the necessary APIs enabled

#### 4. Business Central API Error (401 Unauthorized)
```
Error: The server has rejected the client credentials
```
**Solution**:
- The API key may have expired
- Contact your Business Central administrator to regenerate the API key
- Update the key in `src/backend/src/main/resources/application.properties`

### Checking Service Status

```bash
# Check if services are running
curl http://localhost:8081/actuator/health  # Backend
curl http://localhost:8000/health           # AI Service
curl http://localhost:5173                  # Frontend
```

## Project Structure

```
Backend-B2/
├── src/
│   ├── backend/                 # Spring Boot application
│   │   ├── src/main/java/       # Java source code
│   │   ├── src/main/resources/  # Configuration files
│   │   └── pom.xml             # Maven dependencies
│   ├── frontend/               # React application
│   │   ├── src/                # React source code
│   │   ├── public/             # Static assets
│   │   └── package.json        # NPM dependencies
│   └── gemini-python-service/  # Python AI service
│       ├── app/                # FastAPI application
│       ├── requirements.txt    # Python dependencies
│       └── run_local.py        # Local runner
├── vertex-ai-erp-d0767c2be40d.json  # Vertex AI credentials
└── README.md                   # This file
```

## Support

For technical support or questions:
1. Check the troubleshooting section above
2. Review application logs in the console output
3. Ensure all prerequisites are properly installed
4. Contact the development team for assistance
